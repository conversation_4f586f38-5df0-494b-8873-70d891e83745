{"name": "sail-plus", "version": "5.1.5", "description": "Sail-帆前管理系统", "author": "LionLi", "license": "MIT", "scripts": {"local": "vite serve --mode localhost", "dev": "vite serve --mode development", "build:prod": "vite build --mode production", "build:prod:tsc": "vite build --mode production &&vue-tsc --noEmit", "preview": "vite preview", "lint": "eslint src/**/*.{ts,js,vue} --fix", "prepare": "husky install", "prettier": "prettier --write ."}, "repository": {"type": "git", "url": "https://gitee.com/JavaLionLi/plus-ui.git"}, "dependencies": {"@element-plus/icons-vue": "2.1.0", "@vueup/vue-quill": "1.2.0", "@vueuse/core": "9.5.0", "animate.css": "4.1.1", "await-to-js": "^3.0.0", "axios": "^1.3.4", "echarts": "5.4.0", "element-plus": "2.2.27", "file-saver": "2.0.5", "fuse.js": "6.6.2", "js-cookie": "3.0.1", "jsencrypt": "3.3.1", "crypto-js": "^4.1.1", "nprogress": "0.2.0", "path-browserify": "1.0.1", "path-to-regexp": "6.2.0", "pinia": "2.0.22", "screenfull": "6.0.0", "vform3-builds": "3.0.8", "vue": "3.2.45", "vue-cropper": "1.0.3", "vue-i18n": "9.2.2", "vue-router": "4.1.4", "vue-types": "^5.0.3"}, "devDependencies": {"@iconify/json": "^2.2.40", "@intlify/unplugin-vue-i18n": "0.8.2", "@types/crypto-js": "^4.1.1", "@types/file-saver": "2.0.5", "@types/js-cookie": "3.0.3", "@types/node": "18.14.2", "@types/nprogress": "0.2.0", "@types/path-browserify": "^1.0.0", "@typescript-eslint/eslint-plugin": "5.56.0", "@typescript-eslint/parser": "5.56.0", "@unocss/preset-attributify": "^0.50.6", "@unocss/preset-icons": "^0.50.6", "@unocss/preset-uno": "^0.50.6", "@vitejs/plugin-vue": "4.0.0", "@vue/compiler-sfc": "3.2.45", "autoprefixer": "10.4.14", "eslint": "8.36.0", "eslint-config-prettier": "8.8.0", "eslint-plugin-prettier": "4.2.1", "eslint-plugin-vue": "9.9.0", "fast-glob": "^3.2.11", "husky": "7.0.4", "postcss": "^8.4.21", "prettier": "2.8.6", "sass": "1.56.1", "typescript": "4.9.5", "unocss": "^0.50.6", "unplugin-auto-import": "0.13.0", "unplugin-icons": "0.15.1", "unplugin-vue-components": "0.23.0", "vite": "4.3.1", "vite-plugin-compression": "0.5.1", "vite-plugin-svg-icons": "2.0.1", "unplugin-vue-setup-extend-plus": "0.4.9", "vitest": "^0.29.7", "vue-eslint-parser": "9.1.0", "vue-tsc": "0.35.0"}}